<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>服务资源监控仪表板 | PAAS 资源统计</title>
  <style>
    :root{--bg:#0f355d;--fg:#eaf4ff;--text:#1f2d3d;--muted:#6b7b8c;--card:#fff;--ok:#16a34a;--danger:#ef4444;--border:#e5e7eb;--chip:#f3f4f6;--accent:#1d4ed8}
    *{box-sizing:border-box}body{margin:0;background:#f7fafc;color:var(--text);font:14px/1.45 -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial}
    header{background:var(--bg);color:var(--fg);padding:16px 20px 24px;border-bottom:1px solid rgba(255,255,255,.08)}
    header .title{font-weight:700;font-size:20px}
    header .subtitle{opacity:.85;font-size:12px;margin-top:4px}
    .toolbar{display:flex;gap:8px;align-items:center;margin-top:14px}
    input[type="search"],select,button{border:1px solid var(--border);background:#fff;padding:9px 10px;border-radius:8px}
    input[type="search"]{min-width:260px}
    button{cursor:pointer;background:var(--chip)}
    button.primary{background:var(--accent);color:#fff;border-color:var(--accent)}
    .container{padding:18px 20px}
    .grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:14px}
    .card{background:var(--card);border:1px solid var(--border);border-radius:10px;box-shadow:0 1px 2px rgba(0,0,0,.04)}
    .card-hd{display:flex;justify-content:space-between;align-items:center;padding:12px 14px 8px;border-bottom:1px solid var(--border)}
    .name{font-weight:600}
    .status{font-size:12px;color:#10b981;display:flex;align-items:center;gap:6px}
    .dot{width:8px;height:8px;border-radius:50%;background:#22c55e;display:inline-block}
    .sec{padding:10px 14px;border-bottom:1px dashed var(--border)}
    .sec:last-child{border-bottom:none}
    .sec h4{margin:0 0 8px 0;font-size:13px;font-weight:700;color:#334155}
    .metrics{display:grid;grid-template-columns:repeat(4,1fr);gap:8px}
    .metric{background:#f8fafc;border:1px solid var(--border);border-radius:8px;padding:8px}
    .metric .label{font-size:11px;color:var(--muted)}
    .metric .val{font-weight:700;margin-top:2px}
    .kv{background:#f9fafb;border:1px solid var(--border);border-radius:8px;padding:8px;margin-top:8px;color:#374151;font-size:12px}
    .kv div{display:flex;justify-content:space-between;padding:3px 0}
    .muted{color:var(--muted)}
    .empty{padding:40px;color:var(--muted);text-align:center}
    .skeleton{animation:pulse 1.2s infinite ease-in-out;background:linear-gradient(90deg,#eee,#f5f5f5,#eee);background-size:200% 100%}
    @keyframes pulse{0%{background-position:200% 0}100%{background-position:-200% 0}}
    .flex{display:flex;gap:8px;flex-wrap:wrap}
  </style>
</head>
<body>
  <header>
    <div class="title">服务资源监控仪表板</div>
    <div class="subtitle" id="lastUpdated">就绪</div>
    <div class="toolbar">
      <input id="search" type="search" placeholder="搜索服务名称…" />
      <select id="nsSelect"><option value="">全部命名空间</option></select>
      <button id="reload">按名称排序</button>
      <button class="primary" id="refresh">刷新</button>
      <span class="muted">API 基址:</span>
      <input id="apiBase" style="min-width:260px" placeholder="如 http://*************:30378" />
    </div>
  </header>
  <main class="container">
    <div id="cards" class="grid"></div>
    <div id="empty" class="empty" style="display:none">暂无数据</div>
  </main>

<script>
(function(){
  const DEFAULT_API_BASE=(new URLSearchParams(location.search).get('apiBase'))||'http://*************:30378';
  const PATHS={
    namespaces:'/api/c-tccx4:p-9rk6f/namespaces',
    workloads:(ns)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/workload`,
    container:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/container`,
    cpukline:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/cpukline`,
    cpu:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/cpustats`,
    memkline:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/memkline`,
    mem:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/memstats`
  };
  const el={cards:document.getElementById('cards'),nsSelect:document.getElementById('nsSelect'),search:document.getElementById('search'),refresh:document.getElementById('refresh'),reload:document.getElementById('reload'),last:document.getElementById('lastUpdated'),apiBase:document.getElementById('apiBase'),empty:document.getElementById('empty')};
  el.apiBase.value=DEFAULT_API_BASE;

  function nowStr(){const d=new Date();const p=n=>n.toString().padStart(2,'0');return `${d.getFullYear()}/${p(d.getMonth()+1)}/${p(d.getDate())} ${p(d.getHours())}:${p(d.getMinutes())}:${p(d.getSeconds())}`}
  function setLast(text){el.last.textContent=`数据更新时间 ${nowStr()} | ${text}`}
  function api(path){const base=el.apiBase.value.trim()||DEFAULT_API_BASE;return `${base}${path}`}
  async function jget(url){const r=await fetch(url,{headers:{'Accept':'application/json'},cache:'no-store'});if(!r.ok)throw new Error(`${r.status} ${r.statusText}`);return r.json()}

  function fmtNum(n,prec=5){if(n===null||n===undefined||isNaN(n))return '-';const abs=Math.abs(n);if(abs>=10000)return n.toFixed(0);if(abs>=1000)return n.toFixed(1);if(abs>=1)return n.toFixed(3);return n.toFixed(5)}
  function fmtMB(n){if(n===null||n===undefined||isNaN(n))return '-';if(n>=1024) return (n/1024).toFixed(2)+" GB";return n.toFixed(2)+" MB"}

  function metricBox(label,val){return `<div class="metric"><div class="label">${label}</div><div class="val">${val}</div></div>`}
  function kvRow(k,v){return `<div><span>${k}</span><span>${v}</span></div>`}

  function cardSkeleton(name){return `<div class="card" data-name="${name}"><div class="card-hd"><div class="name">${name}</div><div class="status"><span class="dot"></span>运行中</div></div><div class="sec"><h4>CPU 资源使用情况</h4><div class="metrics"><div class="metric skeleton" style="height:48px"></div><div class="metric skeleton" style="height:48px"></div><div class="metric skeleton" style="height:48px"></div><div class="metric skeleton" style="height:48px"></div></div><div class="kv skeleton" style="height:84px"></div></div><div class="sec"><h4>内存资源使用情况</h4><div class="metrics"><div class="metric skeleton" style="height:48px"></div><div class="metric skeleton" style="height:48px"></div><div class="metric skeleton" style="height:48px"></div><div class="metric skeleton" style="height:48px"></div></div><div class="kv skeleton" style="height:62px"></div></div></div>`}

  function renderCard(ns,name,cpu,mem){
    const cpuBox=[metricBox('最小使用',fmtNum(cpu.cpu_min)),metricBox('平均使用',fmtNum(cpu.cpu_mean)),metricBox('95分位',fmtNum(cpu.cpu95)),metricBox('最大使用',fmtNum(cpu.cpu_max))].join('');
    const memBox=[metricBox('最小使用',fmtMB(mem.mem_min)),metricBox('平均使用',fmtMB(mem.mem_mean)),metricBox('95分位',fmtMB(mem.mem95)),metricBox('最大使用',fmtMB(mem.mem_max))].join('');
    const cpuKV=[
      kvRow('request', cpu.cpu_request||'-'),
      kvRow('limit', cpu.cpu_limit||'-'),
      kvRow('建议', `${cpu.recommend_request??'-'} C  —  ${cpu.recommend_limit??'-'} C`),
      kvRow('碳排', cpu.carbon??'-'),
      kvRow('副本', cpu.replica_num??'-'),
      kvRow('节碳潜力', (cpu.reduced_carbon?cpu.reduced_carbon.toFixed(3)+' 克/小时':'-'))
    ].join('');
    const memKV=[
      kvRow('request', mem.mem_request||'-'),
      kvRow('limit', mem.mem_limit||'-'),
      kvRow('建议', `${mem.recommend_request??'-'} MB  —  ${mem.recommend_limit??'-'} MB`)
    ].join('');
    return `<div class="card" data-name="${name}" data-ns="${ns}">
      <div class="card-hd"><div class="name">${name}</div><div class="status"><span class="dot"></span>运行中</div></div>
      <div class="sec"><h4>CPU 资源使用情况</h4><div class="metrics">${cpuBox}</div><div class="kv">${cpuKV}</div></div>
      <div class="sec"><h4>内存资源使用情况</h4><div class="metrics">${memBox}</div><div class="kv">${memKV}</div></div>
    </div>`
  }

  function setCards(html){el.cards.innerHTML=html;el.empty.style.display=html?"none":"block"}
  function filterCards(){const q=el.search.value.trim().toLowerCase();const ns=el.nsSelect.value;for(const c of el.cards.children){const okName=(c.dataset.name||'').toLowerCase().includes(q);const okNs=!ns||c.dataset.ns===ns;c.style.display=(okName&&okNs)?'block':'none'} }
  function sortCards(){const arr=[...el.cards.children];arr.sort((a,b)=> (a.dataset.name||'').localeCompare(b.dataset.name||''));arr.forEach(n=>el.cards.appendChild(n));filterCards();}

  async function loadNamespaces(){
    try{
      setLast('加载命名空间…');
      const list=await jget(api(PATHS.namespaces));
      el.nsSelect.innerHTML='<option value="">全部命名空间</option>' + list.map(n=>`<option value="${n.Name}">${n.Name}</option>`).join('');
      // 自动加载第一个命名空间
      const def=(new URLSearchParams(location.search).get('ns'))||list?.[0]?.Name||''; if(def){el.nsSelect.value=def}
      await loadNamespace(el.nsSelect.value);
    }catch(err){console.error(err);setCards('');el.empty.textContent='命名空间加载失败：'+err.message;el.empty.style.display='block';setLast('失败')}
  }

  async function loadNamespace(ns){
    try{
      setLast(`加载工作负载（${ns||'全部'}）…`);
      const names=await jget(api(PATHS.workloads(ns)));
      if(!names?.length){setCards('');el.empty.textContent='所选命名空间暂无工作负载';el.empty.style.display='block';setLast('完成');return}
      // 先渲染骨架
      setCards(names.map(n=>cardSkeleton(n)).join(''));
      // 并发获取每个 workload 的统计
      const chunks=(arr,size)=>arr.reduce((acc,_,i)=>i%size?acc:acc.concat([arr.slice(i,i+size)]),[]);
      const results=[]; const batchSize=6;
      for(const batch of chunks(names,batchSize)){
        const res=await Promise.all(batch.map(async w=>{
          try{
            await jget(api(PATHS.container(ns,w)));
            await jget(api(PATHS.cpukline(ns,w)));
            const cpu = await jget(api(PATHS.cpu(ns,w)));
            await jget(api(PATHS.memkline(ns,w)));
            const mem = await jget(api(PATHS.mem(ns,w)));
            return {w,cpu,mem};
          }catch(e){console.warn('获取失败',w,e);return {w,cpu:{cpu_min:0,cpu_mean:0,cpu95:0,cpu_max:0},mem:{mem_min:0,mem_mean:0,mem95:0,mem_max:0,mem_request:'-',mem_limit:'-'}}}
        }));
        results.push(...res);
        // 增量更新 UI
        const html=results.map(r=>renderCard(ns,r.w,r.cpu,r.mem)).join('') + names.slice(results.length).map(n=>cardSkeleton(n)).join('');
        setCards(html); filterCards();
      }
      setLast('完成');
    }catch(err){console.error(err);setCards('');el.empty.textContent='工作负载加载失败：'+err.message;el.empty.style.display='block';setLast('失败')}
  }

  // 事件绑定
  el.refresh.addEventListener('click',()=>loadNamespace(el.nsSelect.value));
  el.reload.addEventListener('click',sortCards);
  el.search.addEventListener('input',filterCards);
  el.nsSelect.addEventListener('change',()=>loadNamespace(el.nsSelect.value));

  // 首次加载
  setLast('初始化…');
  loadNamespaces();
})();
</script>
</body>
</html>

