<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>服务资源监控仪表板 | PAAS 资源统计</title>
  <style>
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      background: #f5f5f5;
      color: #333;
      font-size: 14px;
      line-height: 1.4;
    }

    /* 顶部标题栏 */
    .header {
      background: #2c5aa0;
      color: white;
      text-align: center;
      padding: 20px;
    }
    .header .title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    .header .subtitle {
      font-size: 12px;
      opacity: 0.9;
    }

    /* 工具栏 */
    .toolbar {
      background: #e8e8e8;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }
    .toolbar input, .toolbar select, .toolbar button {
      padding: 6px 12px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 13px;
    }
    .toolbar input[type="search"] {
      min-width: 200px;
    }
    .toolbar button {
      background: #fff;
      cursor: pointer;
    }
    .toolbar button:hover {
      background: #f0f0f0;
    }
    .toolbar button.primary {
      background: #2c5aa0;
      color: white;
      border-color: #2c5aa0;
    }
    .toolbar button.primary:hover {
      background: #1e3f73;
    }

    /* 主容器 */
    .container {
      padding: 20px;
    }

    /* 网格布局 */
    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    /* 卡片样式 */
    .card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    /* 卡片头部 */
    .card-header {
      padding: 15px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .card-header .name {
      font-weight: 600;
      font-size: 15px;
      color: #333;
    }
    .card-header .status {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #28a745;
    }
    .status-dot {
      width: 8px;
      height: 8px;
      background: #28a745;
      border-radius: 50%;
    }

    /* 资源部分 */
    .resource-section {
      padding: 15px;
    }
    .resource-section:not(:last-child) {
      border-bottom: 1px solid #eee;
    }
    .resource-title {
      font-weight: 600;
      font-size: 13px;
      color: #333;
      margin-bottom: 12px;
    }

    /* 指标网格 */
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      margin-bottom: 12px;
    }

    /* 指标框 */
    .metric-box {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 8px;
      position: relative;
      transition: all 0.2s ease;
      min-height: 60px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .metric-box:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .metric-box.red { border-left: 4px solid #dc3545; }
    .metric-box.green { border-left: 4px solid #28a745; }
    .metric-box.blue { border-left: 4px solid #007bff; }
    .metric-box.orange { border-left: 4px solid #fd7e14; }

    .metric-label {
      font-size: 11px;
      color: #666;
      margin-bottom: 2px;
      font-weight: 500;
      line-height: 1.2;
    }
    .metric-value {
      font-weight: 700;
      font-size: 12px;
      color: #333;
      margin-bottom: 2px;
      word-break: break-all;
      line-height: 1.2;
    }
    .metric-usage {
      font-size: 9px;
      color: #888;
      line-height: 1.1;
    }

    /* 使用率进度条 */
    .usage-bar {
      width: 100%;
      height: 3px;
      background: #e9ecef;
      border-radius: 2px;
      margin-top: 4px;
      overflow: hidden;
    }
    .usage-fill {
      height: 100%;
      border-radius: 2px;
      transition: width 0.3s ease;
    }
    .usage-fill.low { background: #28a745; }
    .usage-fill.medium { background: #ffc107; }
    .usage-fill.high { background: #fd7e14; }
    .usage-fill.critical { background: #dc3545; }

    /* 详细信息 */
    .details {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 12px;
      font-size: 12px;
    }
    .detail-row {
      display: flex;
      justify-content: space-between;
      padding: 3px 0;
      border-bottom: 1px solid #e9ecef;
    }
    .detail-row:last-child {
      border-bottom: none;
    }
    .detail-label {
      color: #666;
      font-weight: 500;
    }
    .detail-value {
      color: #333;
      font-weight: 600;
    }
    .detail-value.recommend {
      color: #007bff;
      font-weight: 700;
    }
    .detail-value.warning {
      color: #fd7e14;
    }
    .detail-value.danger {
      color: #dc3545;
    }

    /* 优化建议 */
    .optimization-badge {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
      font-weight: 600;
      margin-left: 8px;
    }
    .optimization-badge.good {
      background: #d4edda;
      color: #155724;
    }
    .optimization-badge.warning {
      background: #fff3cd;
      color: #856404;
    }
    .optimization-badge.danger {
      background: #f8d7da;
      color: #721c24;
    }

    /* 空状态 */
    .empty {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    /* 骨架屏 */
    .skeleton {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    @keyframes loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }
    .skeleton-metric {
      height: 45px;
      border-radius: 4px;
    }
    .skeleton-details {
      height: 80px;
      border-radius: 4px;
      margin-top: 12px;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="title">服务资源监控仪表板</div>
    <div class="subtitle" id="lastUpdated">就绪</div>
  </div>
  <div class="toolbar">
    <input id="search" type="search" placeholder="搜索服务名称…" />
    <select id="nsSelect">
      <option value="">全部命名空间</option>
      <option value="dmsc-prod" selected>dmsc-prod</option>
    </select>
    <button id="reload">按名称排序</button>
    <button class="primary" id="refresh">刷新</button>
    <button id="optimizeAll" style="background: #28a745; color: white; border-color: #28a745;">应用优化建议</button>
    <span style="color: #666;">API 基址:</span>
    <input id="apiBase" style="min-width:260px" placeholder="如 http://*************:30378" value="http://*************:30378" />
  </div>
  <main class="container">
    <div id="cards" class="grid"></div>
    <div id="empty" class="empty" style="display:none">暂无数据</div>
  </main>

<script>
(function(){
  const DEFAULT_API_BASE=(new URLSearchParams(location.search).get('apiBase'))||'http://*************:30378';
  const PATHS={
    namespaces:'/api/c-tccx4:p-9rk6f/namespaces',
    workloads:(ns)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/workload`,
    container:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/container`,
    cpukline:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/cpukline`,
    cpu:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/cpustats`,
    memkline:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/memkline`,
    mem:(ns,w)=>`/api/K8S-Blue-XC-Cloud-kjy/chengdubc-kjy-g01-hcsr05-xc-cluster/${encodeURIComponent(ns)}/${encodeURIComponent(w)}/${encodeURIComponent(w)}/memstats`
  };
  const el={cards:document.getElementById('cards'),nsSelect:document.getElementById('nsSelect'),search:document.getElementById('search'),refresh:document.getElementById('refresh'),reload:document.getElementById('reload'),optimizeAll:document.getElementById('optimizeAll'),last:document.getElementById('lastUpdated'),apiBase:document.getElementById('apiBase'),empty:document.getElementById('empty')};
  el.apiBase.value=DEFAULT_API_BASE;

  function nowStr(){const d=new Date();const p=n=>n.toString().padStart(2,'0');return `${d.getFullYear()}/${p(d.getMonth()+1)}/${p(d.getDate())} ${p(d.getHours())}:${p(d.getMinutes())}:${p(d.getSeconds())}`}
  function setLast(text){el.last.textContent=`数据更新时间 ${nowStr()} | ${text}`}
  function api(path){const base=el.apiBase.value.trim()||DEFAULT_API_BASE;return `${base}${path}`}
  async function jget(url){const r=await fetch(url,{headers:{'Accept':'application/json'},cache:'no-store'});if(!r.ok)throw new Error(`${r.status} ${r.statusText}`);return r.json()}

  function fmtNum(n,prec=5){if(n===null||n===undefined||isNaN(n))return '-';const abs=Math.abs(n);if(abs>=10000)return n.toFixed(0);if(abs>=1000)return n.toFixed(1);if(abs>=1)return n.toFixed(3);return n.toFixed(5)}
  function fmtMB(n){if(n===null||n===undefined||isNaN(n))return '-';if(n>=1024) return (n/1024).toFixed(2)+" GB";return n.toFixed(2)+" MB"}

  function metricBox(label, val, colorClass = 'blue', usage = null, limit = null) {
    let usageBar = '';
    let usageText = '';

    if (usage !== null && limit !== null && limit > 0) {
      const percentage = Math.min((usage / limit) * 100, 100);
      let usageClass = 'low';
      if (percentage > 80) usageClass = 'critical';
      else if (percentage > 60) usageClass = 'high';
      else if (percentage > 40) usageClass = 'medium';

      usageBar = `<div class="usage-bar"><div class="usage-fill ${usageClass}" style="width: ${percentage}%"></div></div>`;
      usageText = `<div class="metric-usage">${percentage.toFixed(1)}% 使用率</div>`;
    }

    return `<div class="metric-box ${colorClass}">
      <div class="metric-label">${label}</div>
      <div class="metric-value">${val}</div>
      ${usageText}
      ${usageBar}
    </div>`
  }
  function detailRow(label, value, valueClass = '', badge = '') {
    return `<div class="detail-row">
      <span class="detail-label">${label}</span>
      <span class="detail-value ${valueClass}">${value}${badge}</span>
    </div>`
  }

  // 计算优化建议
  function getOptimizationAdvice(current, usage, type = 'cpu') {
    if (!current || !usage) return { advice: '-', badge: '', class: '' };

    const currentNum = parseFloat(current.replace(/[^\d.]/g, ''));
    const usageNum = parseFloat(usage.replace(/[^\d.]/g, ''));

    if (usageNum === 0) return { advice: '-', badge: '', class: '' };

    const ratio = usageNum / currentNum;
    let advice, badge, className;

    if (ratio < 0.3) {
      advice = type === 'cpu' ? `${(usageNum * 1.5).toFixed(3)} C` : `${Math.ceil(usageNum * 1.5)} MB`;
      badge = '<span class="optimization-badge warning">可优化</span>';
      className = 'warning';
    } else if (ratio > 0.8) {
      advice = type === 'cpu' ? `${(usageNum * 1.3).toFixed(3)} C` : `${Math.ceil(usageNum * 1.3)} MB`;
      badge = '<span class="optimization-badge danger">需扩容</span>';
      className = 'danger';
    } else {
      advice = current;
      badge = '<span class="optimization-badge good">合理</span>';
      className = 'recommend';
    }

    return { advice, badge, class: className };
  }

  function cardSkeleton(name){
    return `<div class="card" data-name="${name}">
      <div class="card-header">
        <div class="name">${name}</div>
        <div class="status">
          <span class="status-dot"></span>
          运行中
        </div>
      </div>
      <div class="resource-section">
        <div class="resource-title">CPU 资源使用情况</div>
        <div class="metrics-grid">
          <div class="skeleton skeleton-metric"></div>
          <div class="skeleton skeleton-metric"></div>
          <div class="skeleton skeleton-metric"></div>
          <div class="skeleton skeleton-metric"></div>
        </div>
        <div class="skeleton skeleton-details"></div>
      </div>
      <div class="resource-section">
        <div class="resource-title">内存资源使用情况</div>
        <div class="metrics-grid">
          <div class="skeleton skeleton-metric"></div>
          <div class="skeleton skeleton-metric"></div>
          <div class="skeleton skeleton-metric"></div>
          <div class="skeleton skeleton-metric"></div>
        </div>
        <div class="skeleton skeleton-details"></div>
      </div>
    </div>`
  }

  function renderCard(ns, name, cpu, mem) {
    // CPU指标，添加使用率计算
    const cpuLimit = parseFloat((cpu.cpu_limit || '0').replace(/[^\d.]/g, ''));
    const cpuMetrics = [
      metricBox('最小使用', fmtNum(cpu.cpu_min), 'red', cpu.cpu_min, cpuLimit),
      metricBox('平均使用', fmtNum(cpu.cpu_mean), 'green', cpu.cpu_mean, cpuLimit),
      metricBox('95分位', fmtNum(cpu.cpu95), 'blue', cpu.cpu95, cpuLimit),
      metricBox('最大使用', fmtNum(cpu.cpu_max), 'orange', cpu.cpu_max, cpuLimit)
    ].join('');

    // 内存指标，添加使用率计算
    const memLimit = parseFloat((mem.mem_limit || '0').replace(/[^\d.]/g, ''));
    const memMetrics = [
      metricBox('最小使用', fmtMB(mem.mem_min), 'red', mem.mem_min, memLimit),
      metricBox('平均使用', fmtMB(mem.mem_mean), 'green', mem.mem_mean, memLimit),
      metricBox('95分位', fmtMB(mem.mem95), 'blue', mem.mem95, memLimit),
      metricBox('最大使用', fmtMB(mem.mem_max), 'orange', mem.mem_max, memLimit)
    ].join('');

    // CPU详细信息，添加优化建议
    const cpuRequestAdvice = getOptimizationAdvice(cpu.cpu_request, fmtNum(cpu.cpu_mean), 'cpu');
    const cpuLimitAdvice = getOptimizationAdvice(cpu.cpu_limit, fmtNum(cpu.cpu_max), 'cpu');

    const cpuDetails = [
      detailRow('request', cpu.cpu_request || '-'),
      detailRow('limit', cpu.cpu_limit || '-'),
      detailRow('建议 request', cpuRequestAdvice.advice, cpuRequestAdvice.class, cpuRequestAdvice.badge),
      detailRow('建议 limit', cpuLimitAdvice.advice, cpuLimitAdvice.class, cpuLimitAdvice.badge)
    ].join('');

    // 内存详细信息，添加优化建议
    const memRequestAdvice = getOptimizationAdvice(mem.mem_request, fmtMB(mem.mem_mean), 'mem');
    const memLimitAdvice = getOptimizationAdvice(mem.mem_limit, fmtMB(mem.mem_max), 'mem');

    const memDetails = [
      detailRow('request', mem.mem_request || '-'),
      detailRow('limit', mem.mem_limit || '-'),
      detailRow('建议 request', memRequestAdvice.advice, memRequestAdvice.class, memRequestAdvice.badge),
      detailRow('建议 limit', memLimitAdvice.advice, memLimitAdvice.class, memLimitAdvice.badge)
    ].join('');

    return `<div class="card" data-name="${name}" data-ns="${ns}">
      <div class="card-header">
        <div class="name">${name}</div>
        <div class="status">
          <span class="status-dot"></span>
          运行中
        </div>
      </div>
      <div class="resource-section">
        <div class="resource-title">CPU 资源使用情况</div>
        <div class="metrics-grid">${cpuMetrics}</div>
        <div class="details">${cpuDetails}</div>
      </div>
      <div class="resource-section">
        <div class="resource-title">内存资源使用情况</div>
        <div class="metrics-grid">${memMetrics}</div>
        <div class="details">${memDetails}</div>
      </div>
    </div>`
  }

  function setCards(html) {
    el.cards.innerHTML = html;
    el.empty.style.display = html ? "none" : "block";
  }

  function filterCards() {
    const q = el.search.value.trim().toLowerCase();
    const ns = el.nsSelect.value;
    for (const c of el.cards.children) {
      const okName = (c.dataset.name || '').toLowerCase().includes(q);
      const okNs = !ns || c.dataset.ns === ns;
      c.style.display = (okName && okNs) ? 'block' : 'none';
    }
  }

  function sortCards() {
    const arr = [...el.cards.children];
    arr.sort((a, b) => (a.dataset.name || '').localeCompare(b.dataset.name || ''));
    arr.forEach(n => el.cards.appendChild(n));
    filterCards();
  }

  async function loadNamespaces(){
    try{
      setLast('加载命名空间…');
      const list=await jget(api(PATHS.namespaces));
      el.nsSelect.innerHTML='<option value="">全部命名空间</option>' + list.map(n=>`<option value="${n.Name}" ${n.Name==='dmsc-prod'?'selected':''}>${n.Name}</option>`).join('');
      // 自动加载默认命名空间或第一个命名空间
      const def=(new URLSearchParams(location.search).get('ns'))||'dmsc-prod'||list?.[0]?.Name||''; if(def){el.nsSelect.value=def}
      await loadNamespace(el.nsSelect.value);
    }catch(err){console.error(err);setCards('');el.empty.textContent='命名空间加载失败：'+err.message;el.empty.style.display='block';setLast('失败')}
  }

  async function loadNamespace(ns){
    try{
      setLast(`加载工作负载（${ns||'全部'}）…`);
      const names=await jget(api(PATHS.workloads(ns)));
      if(!names?.length){setCards('');el.empty.textContent='所选命名空间暂无工作负载';el.empty.style.display='block';setLast('完成');return}
      // 先渲染骨架
      setCards(names.map(n=>cardSkeleton(n)).join(''));
      // 并发获取每个 workload 的统计
      const chunks=(arr,size)=>arr.reduce((acc,_,i)=>i%size?acc:acc.concat([arr.slice(i,i+size)]),[]);
      const results=[]; const batchSize=6;
      for(const batch of chunks(names,batchSize)){
        const res=await Promise.all(batch.map(async w=>{
          try{
            await jget(api(PATHS.container(ns,w)));
            await jget(api(PATHS.cpukline(ns,w)));
            const cpu = await jget(api(PATHS.cpu(ns,w)));
            await jget(api(PATHS.memkline(ns,w)));
            const mem = await jget(api(PATHS.mem(ns,w)));
            return {w,cpu,mem};
          }catch(e){console.warn('获取失败',w,e);return {w,cpu:{cpu_min:0,cpu_mean:0,cpu95:0,cpu_max:0},mem:{mem_min:0,mem_mean:0,mem95:0,mem_max:0,mem_request:'-',mem_limit:'-'}}}
        }));
        results.push(...res);
        // 增量更新 UI
        const html=results.map(r=>renderCard(ns,r.w,r.cpu,r.mem)).join('') + names.slice(results.length).map(n=>cardSkeleton(n)).join('');
        setCards(html); filterCards();
      }
      setLast('完成');
    }catch(err){console.error(err);setCards('');el.empty.textContent='工作负载加载失败：'+err.message;el.empty.style.display='block';setLast('失败')}
  }

  // 优化建议应用功能
  el.optimizeAll.addEventListener('click', () => {
    const cards = document.querySelectorAll('.card');
    let optimizations = [];

    cards.forEach(card => {
      const name = card.dataset.name;
      const recommendValues = card.querySelectorAll('.detail-value.recommend, .detail-value.warning, .detail-value.danger');
      if (recommendValues.length > 0) {
        optimizations.push(name);
      }
    });

    if (optimizations.length > 0) {
      alert(`发现 ${optimizations.length} 个服务可以优化：\n${optimizations.join(', ')}\n\n建议值已在详细信息中标出，请根据实际情况调整配置。`);
    } else {
      alert('所有服务配置都很合理，暂无需要优化的项目。');
    }
  });

  // 事件绑定
  el.refresh.addEventListener('click',()=>loadNamespace(el.nsSelect.value));
  el.reload.addEventListener('click',sortCards);
  el.search.addEventListener('input',filterCards);
  el.nsSelect.addEventListener('change',()=>loadNamespace(el.nsSelect.value));

  // 首次加载
  setLast('初始化…');
  loadNamespaces();
})();
</script>
</body>
</html>

